#!/usr/bin/env python3
"""
Simple LinkedIn Profile Data Demo

This script creates sample LinkedIn profile data for software and data engineers
and saves it to a CSV file in the Linkedin_Data directory.

This is a demonstration version that doesn't require web scraping dependencies.
For actual scraping, use the full linkedin_scraper.py or the <PERSON><PERSON><PERSON> notebook.

Author: AI Assistant
Date: 2025-07-03
"""

import pandas as pd
import os
from datetime import datetime
import random

# Configuration
OUTPUT_DIR = "Linkedin_Data"
CSV_FILENAME = f"linkedin_profiles_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
CSV_PATH = os.path.join(OUTPUT_DIR, CSV_FILENAME)

# Ensure output directory exists
os.makedirs(OUTPUT_DIR, exist_ok=True)

def create_sample_linkedin_data():
    """Create comprehensive sample LinkedIn profile data"""
    
    # Sample data for software and data engineers
    sample_profiles = [
        {
            'profile_url': 'https://www.linkedin.com/in/john-doe-software-engineer/',
            'name': '<PERSON>',
            'headline': 'Senior Software Engineer at Google',
            'location': 'San Francisco, CA',
            'current_company': 'Google',
            'current_position': 'Senior Software Engineer',
            'experience_years': '~6 positions',
            'education': 'Stanford University - Computer Science; UC Berkeley - MS Computer Science',
            'skills': 'Python; JavaScript; React; Node.js; AWS; Docker; Kubernetes; MongoDB; PostgreSQL; Git',
            'connections': '500+ connections',
            'scraped_at': datetime.now().isoformat()
        },
        {
            'profile_url': 'https://www.linkedin.com/in/jane-smith-data-engineer/',
            'name': 'Jane Smith',
            'headline': 'Data Engineer at Netflix',
            'location': 'Los Angeles, CA',
            'current_company': 'Netflix',
            'current_position': 'Senior Data Engineer',
            'experience_years': '~5 positions',
            'education': 'MIT - Computer Science; Carnegie Mellon University - MS Data Science',
            'skills': 'Python; SQL; Apache Spark; Kafka; Airflow; GCP; BigQuery; Snowflake; dbt; Terraform',
            'connections': '1000+ connections',
            'scraped_at': datetime.now().isoformat()
        },
        {
            'profile_url': 'https://www.linkedin.com/in/alex-johnson-ml-engineer/',
            'name': 'Alex Johnson',
            'headline': 'Machine Learning Engineer at OpenAI',
            'location': 'Seattle, WA',
            'current_company': 'OpenAI',
            'current_position': 'Machine Learning Engineer',
            'experience_years': '~4 positions',
            'education': 'University of Washington - Computer Science; Stanford University - MS AI',
            'skills': 'Python; TensorFlow; PyTorch; Scikit-learn; Kubernetes; MLOps; Docker; AWS; Azure',
            'connections': '750+ connections',
            'scraped_at': datetime.now().isoformat()
        },
        {
            'profile_url': 'https://www.linkedin.com/in/sarah-wilson-fullstack/',
            'name': 'Sarah Wilson',
            'headline': 'Full Stack Software Engineer at Microsoft',
            'location': 'Redmond, WA',
            'current_company': 'Microsoft',
            'current_position': 'Software Engineer II',
            'experience_years': '~4 positions',
            'education': 'University of California, Berkeley - EECS; Georgia Tech - MS Computer Science',
            'skills': 'JavaScript; TypeScript; React; Angular; C#; .NET; Azure; SQL Server; Redis; GraphQL',
            'connections': '600+ connections',
            'scraped_at': datetime.now().isoformat()
        },
        {
            'profile_url': 'https://www.linkedin.com/in/michael-chen-data-scientist/',
            'name': 'Michael Chen',
            'headline': 'Senior Data Scientist at Meta',
            'location': 'Menlo Park, CA',
            'current_company': 'Meta',
            'current_position': 'Senior Data Scientist',
            'experience_years': '~5 positions',
            'education': 'Harvard University - Statistics; MIT - PhD Data Science',
            'skills': 'Python; R; SQL; Tableau; Power BI; Spark; Hadoop; TensorFlow; Pandas; NumPy',
            'connections': '1200+ connections',
            'scraped_at': datetime.now().isoformat()
        },
        {
            'profile_url': 'https://www.linkedin.com/in/emily-rodriguez-backend/',
            'name': 'Emily Rodriguez',
            'headline': 'Backend Software Engineer at Stripe',
            'location': 'San Francisco, CA',
            'current_company': 'Stripe',
            'current_position': 'Software Engineer',
            'experience_years': '~3 positions',
            'education': 'UC San Diego - Computer Engineering; Stanford University - MS Software Engineering',
            'skills': 'Java; Python; Go; Microservices; Kubernetes; AWS; PostgreSQL; Redis; RabbitMQ; Jenkins',
            'connections': '450+ connections',
            'scraped_at': datetime.now().isoformat()
        },
        {
            'profile_url': 'https://www.linkedin.com/in/david-kim-devops/',
            'name': 'David Kim',
            'headline': 'DevOps Engineer at Amazon',
            'location': 'Austin, TX',
            'current_company': 'Amazon',
            'current_position': 'Senior DevOps Engineer',
            'experience_years': '~6 positions',
            'education': 'University of Texas at Austin - Computer Science; Rice University - MS Systems Engineering',
            'skills': 'AWS; Terraform; Ansible; Docker; Kubernetes; Jenkins; Python; Bash; Monitoring; CI/CD',
            'connections': '800+ connections',
            'scraped_at': datetime.now().isoformat()
        },
        {
            'profile_url': 'https://www.linkedin.com/in/lisa-thompson-frontend/',
            'name': 'Lisa Thompson',
            'headline': 'Frontend Software Engineer at Airbnb',
            'location': 'San Francisco, CA',
            'current_company': 'Airbnb',
            'current_position': 'Senior Frontend Engineer',
            'experience_years': '~5 positions',
            'education': 'UCLA - Computer Science; UC Berkeley - MS Human-Computer Interaction',
            'skills': 'JavaScript; TypeScript; React; Vue.js; HTML5; CSS3; Webpack; Jest; Cypress; Figma',
            'connections': '650+ connections',
            'scraped_at': datetime.now().isoformat()
        },
        {
            'profile_url': 'https://www.linkedin.com/in/robert-garcia-data-engineer/',
            'name': 'Robert Garcia',
            'headline': 'Data Engineer at Uber',
            'location': 'San Francisco, CA',
            'current_company': 'Uber',
            'current_position': 'Staff Data Engineer',
            'experience_years': '~7 positions',
            'education': 'UC Berkeley - EECS; Stanford University - MS Data Engineering',
            'skills': 'Python; Scala; Apache Spark; Kafka; Flink; Hadoop; AWS; Snowflake; dbt; Presto',
            'connections': '900+ connections',
            'scraped_at': datetime.now().isoformat()
        },
        {
            'profile_url': 'https://www.linkedin.com/in/amanda-lee-mobile/',
            'name': 'Amanda Lee',
            'headline': 'Mobile Software Engineer at Spotify',
            'location': 'New York, NY',
            'current_company': 'Spotify',
            'current_position': 'Senior iOS Engineer',
            'experience_years': '~4 positions',
            'education': 'NYU - Computer Science; Columbia University - MS Mobile Computing',
            'skills': 'Swift; Objective-C; iOS; Xcode; React Native; Flutter; Firebase; Core Data; TestFlight; Git',
            'connections': '550+ connections',
            'scraped_at': datetime.now().isoformat()
        }
    ]
    
    return sample_profiles

def analyze_data(df):
    """Analyze the LinkedIn profile data"""
    print("LinkedIn Profile Data Analysis")
    print("=" * 50)
    
    # Basic statistics
    print(f"Total profiles: {len(df)}")
    print(f"Unique companies: {df['current_company'].nunique()}")
    print(f"Unique locations: {df['location'].nunique()}")
    
    # Top companies
    print("\nTop Companies:")
    company_counts = df['current_company'].value_counts()
    for company, count in company_counts.head().items():
        print(f"  {company}: {count}")
    
    # Top locations
    print("\nTop Locations:")
    location_counts = df['location'].value_counts()
    for location, count in location_counts.head().items():
        print(f"  {location}: {count}")
    
    # Most common skills
    print("\nMost Common Skills:")
    all_skills = []
    for skills_str in df['skills'].dropna():
        if skills_str != 'N/A':
            skills = [skill.strip() for skill in skills_str.split(';')]
            all_skills.extend(skills)
    
    if all_skills:
        skills_series = pd.Series(all_skills)
        skill_counts = skills_series.value_counts()
        for skill, count in skill_counts.head(10).items():
            print(f"  {skill}: {count}")
    
    # Position types
    print("\nPosition Types:")
    position_types = {}
    for position in df['current_position']:
        if 'Engineer' in position:
            if 'Software' in position or 'Backend' in position or 'Frontend' in position or 'Full Stack' in position:
                position_types['Software Engineer'] = position_types.get('Software Engineer', 0) + 1
            elif 'Data' in position:
                position_types['Data Engineer'] = position_types.get('Data Engineer', 0) + 1
            elif 'Machine Learning' in position or 'ML' in position:
                position_types['ML Engineer'] = position_types.get('ML Engineer', 0) + 1
            elif 'DevOps' in position:
                position_types['DevOps Engineer'] = position_types.get('DevOps Engineer', 0) + 1
            else:
                position_types['Other Engineer'] = position_types.get('Other Engineer', 0) + 1
        elif 'Scientist' in position:
            position_types['Data Scientist'] = position_types.get('Data Scientist', 0) + 1
    
    for pos_type, count in position_types.items():
        print(f"  {pos_type}: {count}")

def main():
    """Main function to create and save LinkedIn profile data"""
    print("LinkedIn Profile Data Generator")
    print("=" * 40)
    print(f"Output directory: {OUTPUT_DIR}")
    print(f"CSV file will be saved as: {CSV_FILENAME}")
    
    # Create sample data
    print("\nGenerating sample LinkedIn profile data...")
    profiles_data = create_sample_linkedin_data()
    
    # Create DataFrame
    df = pd.DataFrame(profiles_data)
    
    # Save to CSV
    try:
        df.to_csv(CSV_PATH, index=False, encoding='utf-8')
        print(f"\nData successfully saved to: {CSV_PATH}")
        
        # Display basic info
        print(f"\nDataset Info:")
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        
        # Show first few rows
        print("\nFirst 3 rows of the data:")
        print(df.head(3).to_string())
        
        # Analyze the data
        print("\n")
        analyze_data(df)
        
        print(f"\n✅ Successfully created LinkedIn profile dataset!")
        print(f"📁 File location: {CSV_PATH}")
        print(f"📊 Total profiles: {len(df)}")
        
    except Exception as e:
        print(f"❌ Error saving data: {e}")

if __name__ == "__main__":
    main()
