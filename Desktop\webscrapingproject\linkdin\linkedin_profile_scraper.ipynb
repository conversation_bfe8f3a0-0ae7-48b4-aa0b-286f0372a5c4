{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# LinkedIn Profile Scraper for Software and Data Engineers\n", "\n", "This notebook scrapes LinkedIn profiles of software engineers and data engineers, extracting key information and saving it to a CSV file.\n", "\n", "**Important Note**: This script is for educational purposes. Always respect LinkedIn's Terms of Service and robots.txt. Consider using LinkedIn's official API for production use."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Required Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "import pandas as pd\n", "import time\n", "import random\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.common.exceptions import TimeoutException, NoSuchElementException\n", "import os\n", "from datetime import datetime\n", "import logging\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration and Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "OUTPUT_DIR = \"Linkedin_Data\"\n", "CSV_FILENAME = f\"linkedin_profiles_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv\"\n", "CSV_PATH = os.path.join(OUTPUT_DIR, CSV_FILENAME)\n", "\n", "# Ensure output directory exists\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "\n", "# Search queries for different engineer types\n", "SEARCH_QUERIES = [\n", "    \"software engineer\",\n", "    \"data engineer\",\n", "    \"senior software engineer\",\n", "    \"senior data engineer\",\n", "    \"python developer\",\n", "    \"machine learning engineer\"\n", "]\n", "\n", "# Headers to mimic a real browser\n", "HEADERS = {\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n", "    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n", "    'Accept-Language': 'en-US,en;q=0.5',\n", "    'Accept-Encoding': 'gzip, deflate',\n", "    'Connection': 'keep-alive',\n", "    'Upgrade-Insecure-Requests': '1',\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. LinkedIn Profile Scraper Class"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class LinkedInProfileScraper:\n", "    def __init__(self, headless=True):\n", "        self.driver = None\n", "        self.profiles_data = []\n", "        self.headless = headless\n", "        self.setup_driver()\n", "    \n", "    def setup_driver(self):\n", "        \"\"\"Setup Chrome WebDriver with appropriate options\"\"\"\n", "        chrome_options = Options()\n", "        if self.headless:\n", "            chrome_options.add_argument(\"--headless\")\n", "        chrome_options.add_argument(\"--no-sandbox\")\n", "        chrome_options.add_argument(\"--disable-dev-shm-usage\")\n", "        chrome_options.add_argument(\"--disable-blink-features=AutomationControlled\")\n", "        chrome_options.add_experimental_option(\"excludeSwitches\", [\"enable-automation\"])\n", "        chrome_options.add_experimental_option('useAutomationExtension', False)\n", "        chrome_options.add_argument(f\"user-agent={HEADERS['User-Agent']}\")\n", "        \n", "        try:\n", "            self.driver = webdriver.Chrome(options=chrome_options)\n", "            self.driver.execute_script(\"Object.defineProperty(navigator, 'webdriver', {get: () => undefined})\")\n", "            logging.info(\"Chrome WebDriver initialized successfully\")\n", "        except Exception as e:\n", "            logging.error(f\"Failed to initialize WebDriver: {e}\")\n", "            raise\n", "    \n", "    def random_delay(self, min_seconds=2, max_seconds=5):\n", "        \"\"\"Add random delay to avoid being detected as a bot\"\"\"\n", "        delay = random.uniform(min_seconds, max_seconds)\n", "        time.sleep(delay)\n", "    \n", "    def extract_profile_info(self, profile_url):\n", "        \"\"\"Extract information from a LinkedIn profile\"\"\"\n", "        try:\n", "            self.driver.get(profile_url)\n", "            self.random_delay(3, 6)\n", "            \n", "            # Wait for page to load\n", "            WebDriverWait(self.driver, 10).until(\n", "                EC.presence_of_element_located((By.TAG_NAME, \"body\"))\n", "            )\n", "            \n", "            profile_data = {\n", "                'profile_url': profile_url,\n", "                'name': self.get_name(),\n", "                'headline': self.get_headline(),\n", "                'location': self.get_location(),\n", "                'current_company': self.get_current_company(),\n", "                'current_position': self.get_current_position(),\n", "                'experience_years': self.get_experience_years(),\n", "                'education': self.get_education(),\n", "                'skills': self.get_skills(),\n", "                'connections': self.get_connections(),\n", "                'scraped_at': datetime.now().isoformat()\n", "            }\n", "            \n", "            return profile_data\n", "            \n", "        except Exception as e:\n", "            logging.error(f\"Error extracting profile info from {profile_url}: {e}\")\n", "            return None\n", "    \n", "    def get_name(self):\n", "        \"\"\"Extract profile name\"\"\"\n", "        try:\n", "            name_selectors = [\n", "                \"h1.text-heading-xlarge\",\n", "                \".pv-text-details__left-panel h1\",\n", "                \".ph5 h1\"\n", "            ]\n", "            for selector in name_selectors:\n", "                try:\n", "                    element = self.driver.find_element(By.CSS_SELECTOR, selector)\n", "                    return element.text.strip()\n", "                except NoSuchElementException:\n", "                    continue\n", "            return \"N/A\"\n", "        except Exception:\n", "            return \"N/A\"\n", "    \n", "    def get_headline(self):\n", "        \"\"\"Extract profile headline\"\"\"\n", "        try:\n", "            headline_selectors = [\n", "                \".text-body-medium.break-words\",\n", "                \".pv-text-details__left-panel .text-body-medium\",\n", "                \".ph5 .text-body-medium\"\n", "            ]\n", "            for selector in headline_selectors:\n", "                try:\n", "                    element = self.driver.find_element(By.CSS_SELECTOR, selector)\n", "                    return element.text.strip()\n", "                except NoSuchElementException:\n", "                    continue\n", "            return \"N/A\"\n", "        except Exception:\n", "            return \"N/A\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Additional Helper Methods"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["    def get_location(self):\n", "        \"\"\"Extract location information\"\"\"\n", "        try:\n", "            location_selectors = [\n", "                \".text-body-small.inline.t-black--light.break-words\",\n", "                \".pv-text-details__left-panel .text-body-small\",\n", "                \".ph5 .text-body-small\"\n", "            ]\n", "            for selector in location_selectors:\n", "                try:\n", "                    element = self.driver.find_element(By.CSS_SELECTOR, selector)\n", "                    return element.text.strip()\n", "                except NoSuchElementException:\n", "                    continue\n", "            return \"N/A\"\n", "        except Exception:\n", "            return \"N/A\"\n", "    \n", "    def get_current_company(self):\n", "        \"\"\"Extract current company\"\"\"\n", "        try:\n", "            company_selectors = [\n", "                \".pv-text-details__right-panel .text-body-medium\",\n", "                \".experience-section .pv-entity__secondary-title\",\n", "                \".pv-top-card--experience-list-item .text-body-medium\"\n", "            ]\n", "            for selector in company_selectors:\n", "                try:\n", "                    element = self.driver.find_element(By.CSS_SELECTOR, selector)\n", "                    return element.text.strip()\n", "                except NoSuchElementException:\n", "                    continue\n", "            return \"N/A\"\n", "        except Exception:\n", "            return \"N/A\"\n", "    \n", "    def get_current_position(self):\n", "        \"\"\"Extract current position\"\"\"\n", "        try:\n", "            position_selectors = [\n", "                \".experience-section .pv-entity__summary-info h3\",\n", "                \".pv-top-card--experience-list-item h3\"\n", "            ]\n", "            for selector in position_selectors:\n", "                try:\n", "                    element = self.driver.find_element(By.CSS_SELECTOR, selector)\n", "                    return element.text.strip()\n", "                except NoSuchElementException:\n", "                    continue\n", "            return \"N/A\"\n", "        except Exception:\n", "            return \"N/A\"\n", "    \n", "    def get_experience_years(self):\n", "        \"\"\"Extract years of experience (estimated)\"\"\"\n", "        try:\n", "            # This is a simplified estimation based on experience section\n", "            experience_elements = self.driver.find_elements(By.CSS_SELECTOR, \".experience-section .pv-entity__date-range\")\n", "            if experience_elements:\n", "                return f\"~{len(experience_elements)} positions\"\n", "            return \"N/A\"\n", "        except Exception:\n", "            return \"N/A\"\n", "    \n", "    def get_education(self):\n", "        \"\"\"Extract education information\"\"\"\n", "        try:\n", "            education_selectors = [\n", "                \".education-section .pv-entity__school-name\",\n", "                \".pv-profile-section .pv-entity__school-name\"\n", "            ]\n", "            for selector in education_selectors:\n", "                try:\n", "                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)\n", "                    if elements:\n", "                        return \"; \".join([elem.text.strip() for elem in elements[:3]])  # Top 3 schools\n", "                except NoSuchElementException:\n", "                    continue\n", "            return \"N/A\"\n", "        except Exception:\n", "            return \"N/A\"\n", "    \n", "    def get_skills(self):\n", "        \"\"\"Extract skills (top skills)\"\"\"\n", "        try:\n", "            skills_selectors = [\n", "                \".skills-section .pv-skill-category-entity__name\",\n", "                \".pv-profile-section .pv-skill-category-entity__name\"\n", "            ]\n", "            for selector in skills_selectors:\n", "                try:\n", "                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)\n", "                    if elements:\n", "                        return \"; \".join([elem.text.strip() for elem in elements[:10]])  # Top 10 skills\n", "                except NoSuchElementException:\n", "                    continue\n", "            return \"N/A\"\n", "        except Exception:\n", "            return \"N/A\"\n", "    \n", "    def get_connections(self):\n", "        \"\"\"Extract number of connections\"\"\"\n", "        try:\n", "            connection_selectors = [\n", "                \".pv-top-card--list-bullet .t-black--light\",\n", "                \".pv-text-details__left-panel .t-black--light\"\n", "            ]\n", "            for selector in connection_selectors:\n", "                try:\n", "                    element = self.driver.find_element(By.CSS_SELECTOR, selector)\n", "                    text = element.text.strip()\n", "                    if \"connection\" in text.lower():\n", "                        return text\n", "                except NoSuchElementException:\n", "                    continue\n", "            return \"N/A\"\n", "        except Exception:\n", "            return \"N/A\"\n", "    \n", "    def search_profiles(self, query, max_profiles=10):\n", "        \"\"\"Search for profiles based on query\"\"\"\n", "        try:\n", "            # Note: This is a simplified approach. In practice, you would need\n", "            # to handle LinkedIn's search pagination and authentication\n", "            search_url = f\"https://www.linkedin.com/search/results/people/?keywords={query.replace(' ', '%20')}\"\n", "            self.driver.get(search_url)\n", "            self.random_delay(3, 6)\n", "            \n", "            # Extract profile URLs from search results\n", "            profile_links = []\n", "            link_elements = self.driver.find_elements(By.CSS_SELECTOR, \"a[href*='/in/']\")\n", "            \n", "            for element in link_elements[:max_profiles]:\n", "                href = element.get_attribute('href')\n", "                if '/in/' in href and href not in profile_links:\n", "                    profile_links.append(href)\n", "            \n", "            return profile_links[:max_profiles]\n", "            \n", "        except Exception as e:\n", "            logging.error(f\"Error searching profiles for query '{query}': {e}\")\n", "            return []\n", "    \n", "    def scrape_profiles(self, profile_urls):\n", "        \"\"\"Scrape multiple profiles\"\"\"\n", "        scraped_profiles = []\n", "        \n", "        for i, url in enumerate(profile_urls, 1):\n", "            logging.info(f\"Scraping profile {i}/{len(profile_urls)}: {url}\")\n", "            \n", "            profile_data = self.extract_profile_info(url)\n", "            if profile_data:\n", "                scraped_profiles.append(profile_data)\n", "                logging.info(f\"Successfully scraped: {profile_data.get('name', 'Unknown')}\")\n", "            \n", "            # Random delay between profiles\n", "            self.random_delay(5, 10)\n", "        \n", "        return scraped_profiles\n", "    \n", "    def save_to_csv(self, profiles_data, filename):\n", "        \"\"\"Save profiles data to CSV file\"\"\"\n", "        try:\n", "            df = pd.DataFrame(profiles_data)\n", "            df.to_csv(filename, index=False, encoding='utf-8')\n", "            logging.info(f\"Data saved to {filename}\")\n", "            return df\n", "        except Exception as e:\n", "            logging.error(f\"Error saving data to CSV: {e}\")\n", "            return None\n", "    \n", "    def close(self):\n", "        \"\"\"Close the WebDriver\"\"\"\n", "        if self.driver:\n", "            self.driver.quit()\n", "            logging.info(\"WebDriver closed\")\n", "\n", "# Add the class methods to the LinkedInProfileScraper class\n", "LinkedInProfileScraper.search_profiles = search_profiles\n", "LinkedInProfileScraper.scrape_profiles = scrape_profiles\n", "LinkedInProfileScraper.save_to_csv = save_to_csv\n", "LinkedInProfileScraper.close = close"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Sample Profile URLs (For Testing)\n", "\n", "Since LinkedIn requires authentication for search, here are some sample profile URLs for testing:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample LinkedIn profile URLs for testing\n", "# Note: Replace these with actual profile URLs you want to scrape\n", "SAMPLE_PROFILE_URLS = [\n", "    # Add actual LinkedIn profile URLs here\n", "    # \"https://www.linkedin.com/in/sample-software-engineer/\",\n", "    # \"https://www.linkedin.com/in/sample-data-engineer/\",\n", "]\n", "\n", "# Alternative: Create mock data for demonstration\n", "def create_sample_data():\n", "    \"\"\"Create sample data for demonstration purposes\"\"\"\n", "    sample_data = [\n", "        {\n", "            'profile_url': 'https://www.linkedin.com/in/sample-software-engineer-1/',\n", "            'name': '<PERSON>',\n", "            'headline': 'Senior Software Engineer at Tech Corp',\n", "            'location': 'San Francisco, CA',\n", "            'current_company': 'Tech Corp',\n", "            'current_position': 'Senior Software Engineer',\n", "            'experience_years': '~5 positions',\n", "            'education': 'Stanford University; UC Berkeley',\n", "            'skills': 'Python; JavaScript; React; Node.js; AWS; Docker',\n", "            'connections': '500+ connections',\n", "            'scraped_at': datetime.now().isoformat()\n", "        },\n", "        {\n", "            'profile_url': 'https://www.linkedin.com/in/sample-data-engineer-1/',\n", "            'name': '<PERSON>',\n", "            'headline': 'Data Engineer at Data Solutions Inc',\n", "            'location': 'New York, NY',\n", "            'current_company': 'Data Solutions Inc',\n", "            'current_position': 'Data Engineer',\n", "            'experience_years': '~4 positions',\n", "            'education': 'MIT; Harvard University',\n", "            'skills': 'Python; SQL; Apache Spark; Kafka; Airflow; GCP',\n", "            'connections': '1000+ connections',\n", "            'scraped_at': datetime.now().isoformat()\n", "        },\n", "        {\n", "            'profile_url': 'https://www.linkedin.com/in/sample-ml-engineer-1/',\n", "            'name': '<PERSON>',\n", "            'headline': 'Machine Learning Engineer at AI Startup',\n", "            'location': 'Seattle, WA',\n", "            'current_company': 'AI Startup',\n", "            'current_position': 'Machine Learning Engineer',\n", "            'experience_years': '~3 positions',\n", "            'education': 'Carnegie Mellon University',\n", "            'skills': '<PERSON>; <PERSON>sor<PERSON><PERSON>; <PERSON>y<PERSON><PERSON><PERSON>; Sc<PERSON>t-learn; Kubernetes; MLOps',\n", "            'connections': '750+ connections',\n", "            'scraped_at': datetime.now().isoformat()\n", "        }\n", "    ]\n", "    return sample_data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Main Scraping Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def main_scraping_workflow(use_sample_data=True, profile_urls=None):\n", "    \"\"\"Main workflow for scraping LinkedIn profiles\"\"\"\n", "    \n", "    if use_sample_data:\n", "        # Use sample data for demonstration\n", "        logging.info(\"Using sample data for demonstration\")\n", "        profiles_data = create_sample_data()\n", "        \n", "        # Save to CSV\n", "        df = pd.DataFrame(profiles_data)\n", "        df.to_csv(CSV_PATH, index=False, encoding='utf-8')\n", "        logging.info(f\"Sample data saved to {CSV_PATH}\")\n", "        \n", "        return df\n", "    \n", "    else:\n", "        # Real scraping workflow\n", "        scraper = None\n", "        try:\n", "            # Initialize scraper\n", "            scraper = LinkedInProfileScraper(headless=False)  # Set to True for headless mode\n", "            \n", "            all_profiles = []\n", "            \n", "            if profile_urls:\n", "                # Scrape provided URLs\n", "                logging.info(f\"Scraping {len(profile_urls)} provided profile URLs\")\n", "                profiles = scraper.scrape_profiles(profile_urls)\n", "                all_profiles.extend(profiles)\n", "            \n", "            else:\n", "                # Search and scrape profiles for each query\n", "                for query in SEARCH_QUERIES:\n", "                    logging.info(f\"Searching profiles for: {query}\")\n", "                    \n", "                    # Search for profiles\n", "                    profile_urls = scraper.search_profiles(query, max_profiles=5)\n", "                    \n", "                    if profile_urls:\n", "                        # Scrape found profiles\n", "                        profiles = scraper.scrape_profiles(profile_urls)\n", "                        all_profiles.extend(profiles)\n", "                    \n", "                    # Delay between different searches\n", "                    scraper.random_delay(10, 15)\n", "            \n", "            # Save results to CSV\n", "            if all_profiles:\n", "                df = scraper.save_to_csv(all_profiles, CSV_PATH)\n", "                logging.info(f\"Successfully scraped {len(all_profiles)} profiles\")\n", "                return df\n", "            else:\n", "                logging.warning(\"No profiles were scraped\")\n", "                return None\n", "                \n", "        except Exception as e:\n", "            logging.error(f\"Error in main scraping workflow: {e}\")\n", "            return None\n", "            \n", "        finally:\n", "            if scraper:\n", "                scraper.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON> the Scraping"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Execute the scraping workflow\n", "print(\"Starting LinkedIn Profile Scraping...\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")\n", "print(f\"CSV file will be saved as: {CSV_FILENAME}\")\n", "\n", "# Run with sample data (set to False for real scraping)\n", "df = main_scraping_workflow(use_sample_data=True)\n", "\n", "if df is not None:\n", "    print(f\"\\nScraping completed successfully!\")\n", "    print(f\"Total profiles scraped: {len(df)}\")\n", "    print(f\"Data saved to: {CSV_PATH}\")\n", "    \n", "    # Display first few rows\n", "    print(\"\\nFirst few rows of the data:\")\n", "    print(df.head())\n", "    \n", "    # Display basic statistics\n", "    print(\"\\nDataset Info:\")\n", "    print(f\"Shape: {df.shape}\")\n", "    print(f\"Columns: {list(df.columns)}\")\n", "    \n", "else:\n", "    print(\"Scraping failed or no data was collected.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Data Analysis and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the saved data for analysis\n", "try:\n", "    df = pd.read_csv(CSV_PATH)\n", "    \n", "    print(\"Data Analysis:\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Basic statistics\n", "    print(f\"Total profiles: {len(df)}\")\n", "    print(f\"Unique companies: {df['current_company'].nunique()}\")\n", "    print(f\"Unique locations: {df['location'].nunique()}\")\n", "    \n", "    # Top companies\n", "    print(\"\\nTop Companies:\")\n", "    print(df['current_company'].value_counts().head())\n", "    \n", "    # Top locations\n", "    print(\"\\nTop Locations:\")\n", "    print(df['location'].value_counts().head())\n", "    \n", "    # Most common skills (if available)\n", "    if 'skills' in df.columns:\n", "        all_skills = []\n", "        for skills_str in df['skills'].dropna():\n", "            if skills_str != 'N/A':\n", "                skills = [skill.strip() for skill in skills_str.split(';')]\n", "                all_skills.extend(skills)\n", "        \n", "        if all_skills:\n", "            skills_df = pd.Series(all_skills).value_counts()\n", "            print(\"\\nTop Skills:\")\n", "            print(skills_df.head(10))\n", "    \n", "except FileNotFoundError:\n", "    print(f\"CSV file not found: {CSV_PATH}\")\n", "except Exception as e:\n", "    print(f\"Error analyzing data: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Important Notes and Disclaimers\n", "\n", "### Legal and Ethical Considerations:\n", "1. **LinkedIn Terms of Service**: Always respect LinkedIn's Terms of Service\n", "2. **Rate Limiting**: Implement proper delays to avoid being blocked\n", "3. **Authentication**: LinkedIn requires login for most search functionality\n", "4. **robots.txt**: Check and respect LinkedIn's robots.txt file\n", "5. **Data Privacy**: Handle personal data responsibly and in compliance with privacy laws\n", "\n", "### Technical Considerations:\n", "1. **WebDriver Setup**: Ensure ChromeDriver is installed and in PATH\n", "2. **Anti-Bot Measures**: LinkedIn has sophisticated anti-bot detection\n", "3. **Dynamic Content**: LinkedIn uses dynamic loading, requiring proper wait strategies\n", "4. **IP Blocking**: Use proxies and rotate user agents for large-scale scraping\n", "\n", "### Recommended Alternatives:\n", "1. **LinkedIn API**: Use official LinkedIn API for legitimate business use\n", "2. **Third-party Services**: Consider services like Apollo, ZoomInfo, or Sales Navigator\n", "3. **Manual Collection**: For small datasets, manual collection might be more appropriate\n", "\n", "### Usage Instructions:\n", "1. Install required packages: `pip install selenium pandas beautifulsoup4 requests`\n", "2. Download ChromeDriver and add to PATH\n", "3. Modify `SAMPLE_PROFILE_URLS` with actual LinkedIn profile URLs\n", "4. Set `use_sample_data=False` in the main function for real scraping\n", "5. Handle authentication if required by LinkedIn"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}